# SystemMonitor Pro - 项目结构文档

## 项目目录结构

```
SystemMonitorPro/
├── SystemMonitorPro.xcodeproj
├── SystemMonitorPro/                    # 主应用目标
│   ├── App/
│   │   ├── SystemMonitorProApp.swift   # 应用入口
│   │   ├── ContentView.swift           # 主界面
│   │   └── AppDelegate.swift           # 应用代理
│   ├── Views/
│   │   ├── Dashboard/
│   │   │   ├── DashboardView.swift     # 仪表盘主视图
│   │   │   ├── MetricCardView.swift    # 指标卡片组件
│   │   │   └── ProgressRingView.swift  # 环形进度条组件
│   │   ├── Details/
│   │   │   ├── CPUDetailView.swift     # CPU详情页
│   │   │   ├── MemoryDetailView.swift  # 内存详情页
│   │   │   ├── StorageDetailView.swift # 存储详情页
│   │   │   └── NetworkDetailView.swift # 网络详情页
│   │   ├── Settings/
│   │   │   ├── SettingsView.swift      # 设置主页
│   │   │   ├── NotificationSettingsView.swift # 通知设置
│   │   │   └── AppearanceSettingsView.swift   # 外观设置
│   │   └── Components/
│   │       ├── ChartView.swift         # 图表组件
│   │       ├── GaugeView.swift         # 仪表盘组件
│   │       └── StatusIndicatorView.swift # 状态指示器
│   ├── Models/
│   │   ├── SystemMetrics.swift         # 系统指标数据模型
│   │   ├── NetworkData.swift           # 网络数据模型
│   │   └── HistoryData.swift           # 历史数据模型
│   ├── Services/
│   │   ├── SystemMonitor.swift         # 系统监控服务
│   │   ├── CPUMonitor.swift           # CPU监控
│   │   ├── MemoryMonitor.swift        # 内存监控
│   │   ├── StorageMonitor.swift       # 存储监控
│   │   ├── NetworkMonitor.swift       # 网络监控
│   │   └── DataManager.swift          # 数据管理
│   ├── Utilities/
│   │   ├── Extensions/
│   │   │   ├── Double+Extensions.swift # 数值扩展
│   │   │   ├── Color+Extensions.swift  # 颜色扩展
│   │   │   └── View+Extensions.swift   # 视图扩展
│   │   ├── Constants.swift             # 常量定义
│   │   ├── Formatters.swift           # 格式化工具
│   │   └── UserDefaults+Keys.swift    # 用户偏好设置
│   └── Resources/
│       ├── Assets.xcassets            # 图片资源
│       ├── Localizable.strings        # 本地化字符串
│       └── Info.plist                 # 应用配置
├── SystemMonitorWidget/               # 小组件扩展
│   ├── SystemMonitorWidget.swift     # 小组件主文件
│   ├── WidgetProvider.swift          # 时间线提供者
│   ├── WidgetEntryView.swift         # 小组件视图
│   ├── WidgetEntry.swift             # 小组件数据模型
│   └── Info.plist                    # 扩展配置
├── SystemMonitorMenuBar/             # 菜单栏扩展
│   ├── MenuBarApp.swift              # 菜单栏应用入口
│   ├── MenuBarView.swift             # 菜单栏视图
│   ├── MenuBarStatusItem.swift       # 状态栏项目
│   └── Info.plist                    # 扩展配置
├── Shared/                           # 共享代码
│   ├── Models/
│   │   └── SharedSystemMetrics.swift # 共享数据模型
│   ├── Services/
│   │   └── SharedDataService.swift   # 共享数据服务
│   └── Extensions/
│       └── SharedExtensions.swift    # 共享扩展
├── Tests/                            # 测试文件
│   ├── SystemMonitorProTests/
│   │   ├── SystemMonitorTests.swift  # 系统监控测试
│   │   ├── DataManagerTests.swift    # 数据管理测试
│   │   └── ModelTests.swift          # 模型测试
│   └── SystemMonitorProUITests/
│       ├── DashboardUITests.swift    # 仪表盘UI测试
│       └── SettingsUITests.swift     # 设置UI测试
└── Documentation/                    # 文档
    ├── API.md                        # API文档
    ├── Architecture.md               # 架构文档
    └── UserGuide.md                  # 用户指南
```

## 核心文件说明

### 1. 主应用文件

#### SystemMonitorProApp.swift
```swift
import SwiftUI

@main
struct SystemMonitorProApp: App {
    @StateObject private var systemMonitor = SystemMonitor()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(systemMonitor)
        }
    }
}
```

#### ContentView.swift
```swift
import SwiftUI

struct ContentView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            DashboardView()
                .tabItem {
                    Image(systemName: "gauge")
                    Text("仪表盘")
                }
                .tag(0)
            
            CPUDetailView()
                .tabItem {
                    Image(systemName: "cpu")
                    Text("CPU")
                }
                .tag(1)
            
            MemoryDetailView()
                .tabItem {
                    Image(systemName: "memorychip")
                    Text("内存")
                }
                .tag(2)
            
            StorageDetailView()
                .tabItem {
                    Image(systemName: "internaldrive")
                    Text("存储")
                }
                .tag(3)
            
            NetworkDetailView()
                .tabItem {
                    Image(systemName: "network")
                    Text("网络")
                }
                .tag(4)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("设置")
                }
                .tag(5)
        }
    }
}
```

### 2. 数据模型

#### SystemMetrics.swift
```swift
import Foundation

struct SystemMetrics: Codable {
    let timestamp: Date
    let cpuUsage: Double
    let memoryUsage: MemoryInfo
    let storageInfo: StorageInfo
    let networkSpeed: NetworkSpeed
    
    struct MemoryInfo: Codable {
        let used: UInt64
        let total: UInt64
        let pressure: MemoryPressure
        
        var usagePercentage: Double {
            return Double(used) / Double(total) * 100
        }
    }
    
    struct StorageInfo: Codable {
        let used: UInt64
        let total: UInt64
        let available: UInt64
        
        var usagePercentage: Double {
            return Double(used) / Double(total) * 100
        }
    }
    
    struct NetworkSpeed: Codable {
        let download: Double // bytes per second
        let upload: Double   // bytes per second
        let connectionType: ConnectionType
    }
    
    enum MemoryPressure: String, Codable {
        case normal = "Normal"
        case warning = "Warning"
        case critical = "Critical"
    }
    
    enum ConnectionType: String, Codable {
        case wifi = "WiFi"
        case cellular = "Cellular"
        case ethernet = "Ethernet"
        case none = "None"
    }
}
```

### 3. 系统监控服务

#### SystemMonitor.swift
```swift
import Foundation
import Combine

class SystemMonitor: ObservableObject {
    @Published var currentMetrics: SystemMetrics?
    @Published var isMonitoring = false
    
    private let cpuMonitor = CPUMonitor()
    private let memoryMonitor = MemoryMonitor()
    private let storageMonitor = StorageMonitor()
    private let networkMonitor = NetworkMonitor()
    private let dataManager = DataManager()
    
    private var timer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupMonitoring()
    }
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.updateMetrics()
        }
    }
    
    func stopMonitoring() {
        isMonitoring = false
        timer?.invalidate()
        timer = nil
    }
    
    private func updateMetrics() {
        let metrics = SystemMetrics(
            timestamp: Date(),
            cpuUsage: cpuMonitor.getCPUUsage(),
            memoryUsage: memoryMonitor.getMemoryInfo(),
            storageInfo: storageMonitor.getStorageInfo(),
            networkSpeed: networkMonitor.getNetworkSpeed()
        )
        
        DispatchQueue.main.async {
            self.currentMetrics = metrics
        }
        
        dataManager.saveMetrics(metrics)
    }
    
    private func setupMonitoring() {
        // 设置监控配置
    }
}
```

### 4. 小组件配置

#### SystemMonitorWidget.swift
```swift
import WidgetKit
import SwiftUI

@main
struct SystemMonitorWidget: Widget {
    let kind: String = "SystemMonitorWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: WidgetProvider()) { entry in
            WidgetEntryView(entry: entry)
        }
        .configurationDisplayName("系统监控")
        .description("实时显示系统性能指标")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}
```

## 开发配置

### 1. Xcode项目设置
- **Deployment Target**: iOS 14.0+
- **Swift Version**: 5.9+
- **Frameworks**: SwiftUI, WidgetKit, Network, Darwin

### 2. 权限配置 (Info.plist)
```xml
<key>NSNetworkUsageDescription</key>
<string>应用需要访问网络信息以监控网络使用情况</string>

<key>UIBackgroundModes</key>
<array>
    <string>background-processing</string>
    <string>background-fetch</string>
</array>
```

### 3. App Groups配置
- 主应用、小组件和菜单栏扩展需要共享数据
- 配置App Groups: `group.com.yourcompany.systemmonitor`

### 4. 构建配置
- **Debug**: 启用调试符号，详细日志
- **Release**: 优化性能，最小化包大小
- **Archive**: 准备App Store发布

## 依赖管理

### Swift Package Manager
```swift
// Package.swift
dependencies: [
    .package(url: "https://github.com/danielgindi/Charts.git", from: "4.1.0"),
    .package(url: "https://github.com/realm/realm-swift.git", from: "10.0.0")
]
```

### 第三方库
- **Charts**: 数据可视化图表
- **Realm**: 本地数据存储（可选）
- **Combine**: 响应式编程（系统自带）

## 构建和部署

### 1. 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd SystemMonitorPro

# 打开Xcode项目
open SystemMonitorPro.xcodeproj

# 运行项目
# 在Xcode中选择目标设备并点击运行
```

### 2. 测试
```bash
# 运行单元测试
xcodebuild test -scheme SystemMonitorPro -destination 'platform=iOS Simulator,name=iPhone 14'

# 运行UI测试
xcodebuild test -scheme SystemMonitorProUITests -destination 'platform=iOS Simulator,name=iPhone 14'
```

### 3. 打包发布
```bash
# 创建Archive
xcodebuild archive -scheme SystemMonitorPro -archivePath SystemMonitorPro.xcarchive

# 导出IPA
xcodebuild -exportArchive -archivePath SystemMonitorPro.xcarchive -exportPath ./Export -exportOptionsPlist ExportOptions.plist
```
