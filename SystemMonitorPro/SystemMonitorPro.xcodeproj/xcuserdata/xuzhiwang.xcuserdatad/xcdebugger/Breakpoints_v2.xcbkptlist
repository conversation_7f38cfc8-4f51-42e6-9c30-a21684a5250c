<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "CB1B6A92-1A71-4D78-AB5E-61BEB6BB46D2"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D0C5F31A-D4DA-488E-833E-7DE1D16A5B0D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "SystemMonitorPro/Services/SystemMonitor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "373"
            endingLineNumber = "373"
            landmarkName = "getNetworkBytes()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5F0FC6AE-2F34-48D7-9C68-8F693C9DC3C7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "SystemMonitorPro/Services/SystemMonitor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "372"
            endingLineNumber = "372"
            landmarkName = "getNetworkBytes()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "98341D2E-2E86-43FE-AC24-604FE8FF5F07"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "SystemMonitorPro/Services/SystemMonitor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "146"
            endingLineNumber = "146"
            landmarkName = "getCPUUsage()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "845869F7-44C6-484F-B787-3A279ADDB99F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "SystemMonitorPro/Services/SystemMonitor.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "349"
            endingLineNumber = "349"
            landmarkName = "getNetworkBytes()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
