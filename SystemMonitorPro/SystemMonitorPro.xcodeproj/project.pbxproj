// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001000000000000001 /* SystemMonitorProApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002000000000000001 /* SystemMonitorProApp.swift */; };
		A1000003000000000000001 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000004000000000000001 /* ContentView.swift */; };
		A1000005000000000000001 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000006000000000000001 /* Assets.xcassets */; };
		A1000007000000000000001 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000008000000000000001 /* Preview Assets.xcassets */; };
		A1000009000000000000001 /* SystemMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000010000000000000001 /* SystemMonitor.swift */; };
		A1000011000000000000001 /* SystemMetrics.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000012000000000000001 /* SystemMetrics.swift */; };
		A1000013000000000000001 /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000014000000000000001 /* DashboardView.swift */; };
		A1000015000000000000001 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1000016000000000000001 /* WidgetKit.framework */; };
		A1000017000000000000001 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1000018000000000000001 /* SwiftUI.framework */; };
		A1000019000000000000001 /* SystemMonitorWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000020000000000000001 /* SystemMonitorWidget.swift */; };
		A1000021000000000000001 /* SystemMonitorWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = A1000022000000000000001 /* SystemMonitorWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A1000023000000000000001 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A1000024000000000000001 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A1000025000000000000001;
			remoteInfo = SystemMonitorWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		A1000026000000000000001 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				A1000021000000000000001 /* SystemMonitorWidgetExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		A1000027000000000000001 /* SystemMonitorPro.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SystemMonitorPro.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000002000000000000001 /* SystemMonitorProApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemMonitorProApp.swift; sourceTree = "<group>"; };
		A1000004000000000000001 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1000006000000000000001 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1000008000000000000001 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1000010000000000000001 /* SystemMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemMonitor.swift; sourceTree = "<group>"; };
		A1000012000000000000001 /* SystemMetrics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemMetrics.swift; sourceTree = "<group>"; };
		A1000014000000000000001 /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
		A1000016000000000000001 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		A1000018000000000000001 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		A1000020000000000000001 /* SystemMonitorWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemMonitorWidget.swift; sourceTree = "<group>"; };
		A1000022000000000000001 /* SystemMonitorWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SystemMonitorWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000028000000000000001 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1000029000000000000001 /* SystemMonitorPro.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SystemMonitorPro.entitlements; sourceTree = "<group>"; };
		A1000030000000000000001 /* SystemMonitorWidgetExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SystemMonitorWidgetExtension.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1000031000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1000032000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000017000000000000001 /* SwiftUI.framework in Frameworks */,
				A1000015000000000000001 /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1000033000000000000001 = {
			isa = PBXGroup;
			children = (
				A1000034000000000000001 /* SystemMonitorPro */,
				A1000035000000000000001 /* SystemMonitorWidget */,
				A1000036000000000000001 /* Shared */,
				A1000037000000000000001 /* Frameworks */,
				A1000038000000000000001 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000034000000000000001 /* SystemMonitorPro */ = {
			isa = PBXGroup;
			children = (
				A1000029000000000000001 /* SystemMonitorPro.entitlements */,
				A1000039000000000000001 /* App */,
				A1000040000000000000001 /* Views */,
				A1000041000000000000001 /* Models */,
				A1000042000000000000001 /* Services */,
				A1000043000000000000001 /* Utilities */,
				A1000044000000000000001 /* Resources */,
			);
			path = SystemMonitorPro;
			sourceTree = "<group>";
		};
		A1000035000000000000001 /* SystemMonitorWidget */ = {
			isa = PBXGroup;
			children = (
				A1000030000000000000001 /* SystemMonitorWidgetExtension.entitlements */,
				A1000020000000000000001 /* SystemMonitorWidget.swift */,
				A1000028000000000000001 /* Info.plist */,
			);
			path = SystemMonitorWidget;
			sourceTree = "<group>";
		};
		A1000036000000000000001 /* Shared */ = {
			isa = PBXGroup;
			children = (
				A1000045000000000000001 /* Models */,
				A1000046000000000000001 /* Services */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		A1000037000000000000001 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A1000016000000000000001 /* WidgetKit.framework */,
				A1000018000000000000001 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A1000038000000000000001 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1000027000000000000001 /* SystemMonitorPro.app */,
				A1000022000000000000001 /* SystemMonitorWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1000039000000000000001 /* App */ = {
			isa = PBXGroup;
			children = (
				A1000002000000000000001 /* SystemMonitorProApp.swift */,
				A1000004000000000000001 /* ContentView.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		A1000040000000000000001 /* Views */ = {
			isa = PBXGroup;
			children = (
				A1000047000000000000001 /* Dashboard */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1000041000000000000001 /* Models */ = {
			isa = PBXGroup;
			children = (
				A1000012000000000000001 /* SystemMetrics.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1000042000000000000001 /* Services */ = {
			isa = PBXGroup;
			children = (
				A1000010000000000000001 /* SystemMonitor.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1000043000000000000001 /* Utilities */ = {
			isa = PBXGroup;
			children = (
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		A1000044000000000000001 /* Resources */ = {
			isa = PBXGroup;
			children = (
				A1000006000000000000001 /* Assets.xcassets */,
				A1000048000000000000001 /* Preview Content */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		A1000045000000000000001 /* Models */ = {
			isa = PBXGroup;
			children = (
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1000046000000000000001 /* Services */ = {
			isa = PBXGroup;
			children = (
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1000047000000000000001 /* Dashboard */ = {
			isa = PBXGroup;
			children = (
				A1000014000000000000001 /* DashboardView.swift */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		A1000048000000000000001 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1000008000000000000001 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1000049000000000000001 /* SystemMonitorPro */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1000050000000000000001 /* Build configuration list for PBXNativeTarget "SystemMonitorPro" */;
			buildPhases = (
				A1000051000000000000001 /* Sources */,
				A1000031000000000000001 /* Frameworks */,
				A1000052000000000000001 /* Resources */,
				A1000026000000000000001 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				A1000053000000000000001 /* PBXTargetDependency */,
			);
			name = SystemMonitorPro;
			productName = SystemMonitorPro;
			productReference = A1000027000000000000001 /* SystemMonitorPro.app */;
			productType = "com.apple.product-type.application";
		};
		A1000025000000000000001 /* SystemMonitorWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1000054000000000000001 /* Build configuration list for PBXNativeTarget "SystemMonitorWidgetExtension" */;
			buildPhases = (
				A1000055000000000000001 /* Sources */,
				A1000032000000000000001 /* Frameworks */,
				A1000056000000000000001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SystemMonitorWidgetExtension;
			productName = SystemMonitorWidgetExtension;
			productReference = A1000022000000000000001 /* SystemMonitorWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1000024000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1000049000000000000001 = {
						CreatedOnToolsVersion = 15.0;
					};
					A1000025000000000000001 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1000057000000000000001 /* Build configuration list for PBXProject "SystemMonitorPro" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1000033000000000000001;
			productRefGroup = A1000038000000000000001 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1000049000000000000001 /* SystemMonitorPro */,
				A1000025000000000000001 /* SystemMonitorWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1000052000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000007000000000000001 /* Preview Assets.xcassets in Resources */,
				A1000005000000000000001 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1000056000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1000051000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000003000000000000001 /* ContentView.swift in Sources */,
				A1000013000000000000001 /* DashboardView.swift in Sources */,
				A1000011000000000000001 /* SystemMetrics.swift in Sources */,
				A1000009000000000000001 /* SystemMonitor.swift in Sources */,
				A1000001000000000000001 /* SystemMonitorProApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1000055000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000019000000000000001 /* SystemMonitorWidget.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A1000053000000000000001 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A1000025000000000000001 /* SystemMonitorWidgetExtension */;
			targetProxy = A1000023000000000000001 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A1000058000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1000059000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A1000060000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SystemMonitorPro/SystemMonitorPro.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SystemMonitorPro/Resources/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.systemmonitor.SystemMonitorPro;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A1000061000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SystemMonitorPro/SystemMonitorPro.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SystemMonitorPro/Resources/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.systemmonitor.SystemMonitorPro;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A1000062000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = SystemMonitorWidget/SystemMonitorWidgetExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SystemMonitorWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SystemMonitorWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.systemmonitor.SystemMonitorPro.SystemMonitorWidgetExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A1000063000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = SystemMonitorWidget/SystemMonitorWidgetExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SystemMonitorWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SystemMonitorWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.systemmonitor.SystemMonitorPro.SystemMonitorWidgetExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1000057000000000000001 /* Build configuration list for PBXProject "SystemMonitorPro" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000058000000000000001 /* Debug */,
				A1000059000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1000050000000000000001 /* Build configuration list for PBXNativeTarget "SystemMonitorPro" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000060000000000000001 /* Debug */,
				A1000061000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1000054000000000000001 /* Build configuration list for PBXNativeTarget "SystemMonitorWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000062000000000000001 /* Debug */,
				A1000063000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1000024000000000000001 /* Project object */;
}