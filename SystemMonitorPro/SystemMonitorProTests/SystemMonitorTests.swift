//
//  SystemMonitorTests.swift
//  SystemMonitorProTests
//
//  Created by System on 2025-07-07.
//

import XCTest
@testable import SystemMonitorPro

final class SystemMonitorTests: XCTestCase {
    
    var systemMonitor: SystemMonitor!
    
    override func setUpWithError() throws {
        systemMonitor = SystemMonitor()
    }
    
    override func tearDownWithError() throws {
        systemMonitor.stopMonitoring()
        systemMonitor = nil
    }
    
    func testSystemMonitorInitialization() throws {
        XCTAssertNotNil(systemMonitor)
        XCTAssertNil(systemMonitor.currentMetrics)
    }
    
    func testStartMonitoring() throws {
        systemMonitor.startMonitoring()
        
        // Wait a bit for the first metrics to be collected
        let expectation = XCTestExpectation(description: "Metrics collected")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 3.0)
        
        XCTAssertNotNil(systemMonitor.currentMetrics)
        
        if let metrics = systemMonitor.currentMetrics {
            // Test CPU metrics
            XCTAssertGreaterThanOrEqual(metrics.cpuUsage, 0.0)
            XCTAssertLessThanOrEqual(metrics.cpuUsage, 100.0)
            
            // Test Memory metrics
            XCTAssertGreaterThan(metrics.memoryUsage.total, 0)
            XCTAssertGreaterThanOrEqual(metrics.memoryUsage.used, 0)
            XCTAssertLessThanOrEqual(metrics.memoryUsage.used, metrics.memoryUsage.total)
            XCTAssertGreaterThanOrEqual(metrics.memoryUsage.usagePercentage, 0.0)
            XCTAssertLessThanOrEqual(metrics.memoryUsage.usagePercentage, 100.0)
            
            // Test Storage metrics
            XCTAssertGreaterThan(metrics.storageInfo.total, 0)
            XCTAssertGreaterThanOrEqual(metrics.storageInfo.used, 0)
            XCTAssertLessThanOrEqual(metrics.storageInfo.used, metrics.storageInfo.total)
            XCTAssertGreaterThanOrEqual(metrics.storageInfo.usagePercentage, 0.0)
            XCTAssertLessThanOrEqual(metrics.storageInfo.usagePercentage, 100.0)
            
            // Test Network metrics
            XCTAssertGreaterThanOrEqual(metrics.networkSpeed.downloadSpeed, 0.0)
            XCTAssertGreaterThanOrEqual(metrics.networkSpeed.uploadSpeed, 0.0)
        }
    }
    
    func testStopMonitoring() throws {
        systemMonitor.startMonitoring()
        
        // Wait a bit for monitoring to start
        let startExpectation = XCTestExpectation(description: "Monitoring started")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            startExpectation.fulfill()
        }
        wait(for: [startExpectation], timeout: 2.0)
        
        systemMonitor.stopMonitoring()
        
        // Wait a bit to ensure monitoring has stopped
        let stopExpectation = XCTestExpectation(description: "Monitoring stopped")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            stopExpectation.fulfill()
        }
        wait(for: [stopExpectation], timeout: 2.0)
        
        // The metrics should still be available but not updating
        XCTAssertNotNil(systemMonitor.currentMetrics)
    }
    
    func testFormatBytesFunction() throws {
        // Test the formatBytes function used in the UI
        let testCases: [(UInt64, String)] = [
            (0, "0 B"),
            (1023, "1023 B"),
            (1024, "1.0 KB"),
            (1536, "1.5 KB"),
            (1048576, "1.0 MB"),
            (1073741824, "1.0 GB"),
            (1099511627776, "1.0 TB")
        ]
        
        for (bytes, expected) in testCases {
            let result = formatBytes(bytes)
            XCTAssertEqual(result, expected, "formatBytes(\(bytes)) should return \(expected), but got \(result)")
        }
    }
}

// Helper function to test formatBytes
func formatBytes(_ bytes: UInt64) -> String {
    let units = ["B", "KB", "MB", "GB", "TB"]
    var size = Double(bytes)
    var unitIndex = 0
    
    while size >= 1024 && unitIndex < units.count - 1 {
        size /= 1024
        unitIndex += 1
    }
    
    if unitIndex == 0 {
        return "\(Int(size)) \(units[unitIndex])"
    } else {
        return String(format: "%.1f %@", size, units[unitIndex])
    }
}
