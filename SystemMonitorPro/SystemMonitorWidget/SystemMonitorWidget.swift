import WidgetKit
import SwiftUI

// MARK: - Widget Entry
struct SystemMonitorEntry: TimelineEntry {
    let date: Date
    let cpuUsage: Double
    let memoryUsage: Double
    let memoryTotal: UInt64
    let storageUsage: Double
    let networkDownload: Double
    let networkUpload: Double
    let connectionType: String
}

// MARK: - Timeline Provider
struct SystemMonitorProvider: TimelineProvider {
    func placeholder(in context: Context) -> SystemMonitorEntry {
        SystemMonitorEntry(
            date: Date(),
            cpuUsage: 45.0,
            memoryUsage: 60.0,
            memoryTotal: 8_000_000_000,
            storageUsage: 55.0,
            networkDownload: 12_500_000,
            networkUpload: 2_100_000,
            connectionType: "WiFi"
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (SystemMonitorEntry) -> ()) {
        let entry = placeholder(in: context)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<SystemMonitorEntry>) -> ()) {
        var entries: [SystemMonitorEntry] = []
        
        // 生成当前时间的条目
        let currentDate = Date()
        let entry = SystemMonitorEntry(
            date: currentDate,
            cpuUsage: Double.random(in: 20...80),
            memoryUsage: Double.random(in: 40...85),
            memoryTotal: 8_000_000_000,
            storageUsage: Double.random(in: 30...70),
            networkDownload: Double.random(in: 1_000_000...50_000_000),
            networkUpload: Double.random(in: 100_000...10_000_000),
            connectionType: "WiFi"
        )
        entries.append(entry)
        
        // 创建时间线，每30秒更新一次
        let timeline = Timeline(entries: entries, policy: .after(currentDate.addingTimeInterval(30)))
        completion(timeline)
    }
}

// MARK: - Widget Views
struct SystemMonitorWidgetEntryView: View {
    var entry: SystemMonitorProvider.Entry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(entry: entry)
        case .systemMedium:
            MediumWidgetView(entry: entry)
        case .systemLarge:
            LargeWidgetView(entry: entry)
        default:
            SmallWidgetView(entry: entry)
        }
    }
}

// MARK: - Small Widget (CPU Only)
struct SmallWidgetView: View {
    let entry: SystemMonitorEntry
    
    var body: some View {
        VStack(spacing: 8) {
            Text("CPU")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            ZStack {
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: 8)
                
                Circle()
                    .trim(from: 0, to: entry.cpuUsage / 100)
                    .stroke(
                        cpuColor(for: entry.cpuUsage),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .rotationEffect(.degrees(-90))
                
                Text("\(entry.cpuUsage, specifier: "%.0f")%")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            .frame(width: 60, height: 60)
            
            Text("使用率")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private func cpuColor(for usage: Double) -> Color {
        switch usage {
        case 0..<50: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }
}

// MARK: - Medium Widget (CPU, Memory, Network)
struct MediumWidgetView: View {
    let entry: SystemMonitorEntry
    
    var body: some View {
        HStack(spacing: 16) {
            // CPU
            VStack(spacing: 4) {
                Text("CPU")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                ZStack {
                    Circle()
                        .stroke(Color(.systemGray5), lineWidth: 6)
                    
                    Circle()
                        .trim(from: 0, to: entry.cpuUsage / 100)
                        .stroke(
                            cpuColor(for: entry.cpuUsage),
                            style: StrokeStyle(lineWidth: 6, lineCap: .round)
                        )
                        .rotationEffect(.degrees(-90))
                    
                    Text("\(entry.cpuUsage, specifier: "%.0f")%")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .frame(width: 40, height: 40)
            }
            
            // Memory
            VStack(spacing: 4) {
                Text("内存")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                ZStack {
                    Circle()
                        .stroke(Color(.systemGray5), lineWidth: 6)
                    
                    Circle()
                        .trim(from: 0, to: entry.memoryUsage / 100)
                        .stroke(
                            memoryColor(for: entry.memoryUsage),
                            style: StrokeStyle(lineWidth: 6, lineCap: .round)
                        )
                        .rotationEffect(.degrees(-90))
                    
                    Text("\(entry.memoryUsage, specifier: "%.0f")%")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .frame(width: 40, height: 40)
            }
            
            // Network
            VStack(spacing: 4) {
                Text("网络")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                VStack(spacing: 2) {
                    HStack(spacing: 2) {
                        Image(systemName: "arrow.down")
                            .font(.caption2)
                            .foregroundColor(.blue)
                        Text(formatSpeed(entry.networkDownload))
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                    
                    HStack(spacing: 2) {
                        Image(systemName: "arrow.up")
                            .font(.caption2)
                            .foregroundColor(.green)
                        Text(formatSpeed(entry.networkUpload))
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                }
                .frame(width: 40, height: 40)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private func cpuColor(for usage: Double) -> Color {
        switch usage {
        case 0..<50: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }
    
    private func memoryColor(for usage: Double) -> Color {
        switch usage {
        case 0..<60: return .green
        case 60..<85: return .orange
        default: return .red
        }
    }
}

// MARK: - Large Widget (Complete Dashboard)
struct LargeWidgetView: View {
    let entry: SystemMonitorEntry
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Text("系统监控")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(entry.date, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Metrics Grid
            HStack(spacing: 16) {
                // CPU
                VStack(spacing: 8) {
                    Text("CPU")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    ZStack {
                        Circle()
                            .stroke(Color(.systemGray5), lineWidth: 8)
                        
                        Circle()
                            .trim(from: 0, to: entry.cpuUsage / 100)
                            .stroke(
                                cpuColor(for: entry.cpuUsage),
                                style: StrokeStyle(lineWidth: 8, lineCap: .round)
                            )
                            .rotationEffect(.degrees(-90))
                        
                        Text("\(entry.cpuUsage, specifier: "%.0f")%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    .frame(width: 50, height: 50)
                }
                
                // Memory
                VStack(spacing: 8) {
                    Text("内存")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    ZStack {
                        Circle()
                            .stroke(Color(.systemGray5), lineWidth: 8)
                        
                        Circle()
                            .trim(from: 0, to: entry.memoryUsage / 100)
                            .stroke(
                                memoryColor(for: entry.memoryUsage),
                                style: StrokeStyle(lineWidth: 8, lineCap: .round)
                            )
                            .rotationEffect(.degrees(-90))
                        
                        Text("\(entry.memoryUsage, specifier: "%.0f")%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    .frame(width: 50, height: 50)
                }
                
                // Storage
                VStack(spacing: 8) {
                    Text("存储")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    ZStack {
                        Circle()
                            .stroke(Color(.systemGray5), lineWidth: 8)
                        
                        Circle()
                            .trim(from: 0, to: entry.storageUsage / 100)
                            .stroke(
                                storageColor(for: entry.storageUsage),
                                style: StrokeStyle(lineWidth: 8, lineCap: .round)
                            )
                            .rotationEffect(.degrees(-90))
                        
                        Text("\(entry.storageUsage, specifier: "%.0f")%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    .frame(width: 50, height: 50)
                }
                
                Spacer()
            }
            
            // Network Section
            VStack(spacing: 8) {
                HStack {
                    Text("网络状态")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(entry.connectionType)
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.down")
                            .font(.caption)
                            .foregroundColor(.blue)
                        Text("下载: \(formatSpeed(entry.networkDownload))")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.up")
                            .font(.caption)
                            .foregroundColor(.green)
                        Text("上传: \(formatSpeed(entry.networkUpload))")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private func cpuColor(for usage: Double) -> Color {
        switch usage {
        case 0..<50: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }
    
    private func memoryColor(for usage: Double) -> Color {
        switch usage {
        case 0..<60: return .green
        case 60..<85: return .orange
        default: return .red
        }
    }
    
    private func storageColor(for usage: Double) -> Color {
        switch usage {
        case 0..<70: return .green
        case 70..<90: return .orange
        default: return .red
        }
    }
}

// MARK: - Helper Functions
private func formatSpeed(_ bytesPerSecond: Double) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB]
    formatter.countStyle = .memory
    return formatter.string(fromByteCount: Int64(bytesPerSecond)) + "/s"
}

// MARK: - Widget Configuration
@main
struct SystemMonitorWidget: Widget {
    let kind: String = "SystemMonitorWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SystemMonitorProvider()) { entry in
            if #available(iOS 17.0, *) {
                SystemMonitorWidgetEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                SystemMonitorWidgetEntryView(entry: entry)
                    .padding()
                    .background(Color(.systemBackground))
            }
        }
        .configurationDisplayName("系统监控")
        .description("实时显示CPU、内存、存储和网络使用情况")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}
