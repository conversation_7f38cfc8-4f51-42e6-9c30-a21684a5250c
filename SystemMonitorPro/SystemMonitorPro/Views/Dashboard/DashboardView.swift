import SwiftUI

struct DashboardView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    @State private var showingDetails = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // 标题和状态
                    headerSection
                    
                    // 主要指标卡片
                    if let metrics = systemMonitor.currentMetrics {
                        metricsGrid(metrics: metrics)
                    } else {
                        loadingView
                    }
                    
                    // 快速操作按钮
                    quickActionsSection
                }
                .padding()
            }
            .navigationTitle("系统监控")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                systemMonitor.updateMetrics()
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "desktopcomputer")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading) {
                    Text("设备状态")
                        .font(.headline)
                    
                    if let metrics = systemMonitor.currentMetrics {
                        HStack {
                            Image(systemName: metrics.overallHealthStatus.systemImageName)
                                .foregroundColor(Color(metrics.overallHealthStatus.color))
                            Text(metrics.overallHealthStatus.rawValue)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("实时监控")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        Circle()
                            .fill(systemMonitor.isMonitoring ? .green : .red)
                            .frame(width: 8, height: 8)
                        
                        Text(systemMonitor.isMonitoring ? "运行中" : "已停止")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    private func metricsGrid(metrics: SystemMetrics) -> some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            // CPU卡片
            MetricCardView(
                title: "CPU",
                value: String(format: "%.1f%%", metrics.cpuUsage),
                progress: metrics.cpuUsage / 100,
                color: cpuColor(for: metrics.cpuUsage),
                icon: "cpu",
                subtitle: "处理器使用率"
            )
            
            // 内存卡片
            MetricCardView(
                title: "内存",
                value: String(format: "%.1f%%", metrics.memoryUsage.usagePercentage),
                progress: metrics.memoryUsage.usagePercentage / 100,
                color: memoryColor(for: metrics.memoryUsage.usagePercentage),
                icon: "memorychip",
                subtitle: formatBytes(metrics.memoryUsage.used)
            )
            
            // 存储卡片
            MetricCardView(
                title: "存储",
                value: String(format: "%.1f%%", metrics.storageInfo.usagePercentage),
                progress: metrics.storageInfo.usagePercentage / 100,
                color: storageColor(for: metrics.storageInfo.usagePercentage),
                icon: "internaldrive",
                subtitle: "可用 \(formatBytes(metrics.storageInfo.available))"
            )
            
            // 网络卡片
            NetworkCardView(networkSpeed: metrics.networkSpeed)
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("正在加载系统信息...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(height: 200)
    }
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("快速操作")
                .font(.headline)
                .padding(.horizontal)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                QuickActionButton(
                    title: "刷新数据",
                    icon: "arrow.clockwise",
                    color: .blue
                ) {
                    systemMonitor.updateMetrics()
                }
                
                QuickActionButton(
                    title: "清除历史",
                    icon: "trash",
                    color: .red
                ) {
                    systemMonitor.clearHistory()
                }
                
                QuickActionButton(
                    title: "设置",
                    icon: "gear",
                    color: .gray
                ) {
                    showingDetails = true
                }
            }
            .padding(.horizontal)
        }
    }
    
    // MARK: - 辅助方法
    
    private func cpuColor(for usage: Double) -> Color {
        switch usage {
        case 0..<50:
            return .green
        case 50..<80:
            return .orange
        default:
            return .red
        }
    }
    
    private func memoryColor(for usage: Double) -> Color {
        switch usage {
        case 0..<60:
            return .green
        case 60..<85:
            return .orange
        default:
            return .red
        }
    }
    
    private func storageColor(for usage: Double) -> Color {
        switch usage {
        case 0..<70:
            return .green
        case 70..<90:
            return .orange
        default:
            return .red
        }
    }
}

// MARK: - 子视图组件

struct MetricCardView: View {
    let title: String
    let value: String
    let progress: Double
    let color: Color
    let icon: String
    let subtitle: String
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                VStack(alignment: .leading) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            ZStack {
                Circle()
                    .stroke(color.opacity(0.3), lineWidth: 8)
                    .frame(width: 80, height: 80)

                Circle()
                    .trim(from: 0, to: CGFloat(progress))
                    .stroke(color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .rotationEffect(.degrees(-90))
                    .frame(width: 80, height: 80)
                    .animation(.easeInOut(duration: 0.5), value: progress)
            }
            .overlay(
                Text(value)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            )
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct NetworkCardView: View {
    let networkSpeed: SystemMetrics.NetworkSpeed
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: networkSpeed.connectionType.systemImageName)
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading) {
                    Text("网络")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(networkSpeed.connectionType.rawValue)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "arrow.down")
                        .foregroundColor(.blue)
                    Text(formatSpeed(networkSpeed.download))
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                }
                
                HStack {
                    Image(systemName: "arrow.up")
                        .foregroundColor(.green)
                    Text(formatSpeed(networkSpeed.upload))
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 辅助函数

private func formatBytes(_ bytes: UInt64) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useGB, .useMB]
    formatter.countStyle = .memory
    return formatter.string(fromByteCount: Int64(bytes))
}

private func formatSpeed(_ bytesPerSecond: Double) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB]
    formatter.countStyle = .memory
    return formatter.string(fromByteCount: Int64(bytesPerSecond)) + "/s"
}

#Preview {
    DashboardView()
        .environmentObject(SystemMonitor())
}
