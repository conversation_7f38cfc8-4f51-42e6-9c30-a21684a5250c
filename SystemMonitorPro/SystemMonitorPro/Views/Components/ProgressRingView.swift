import SwiftUI

struct ProgressRingView: View {
    let progress: Double
    let color: Color
    let lineWidth: CGFloat
    let backgroundColor: Color
    
    @State private var animatedProgress: Double = 0
    
    init(progress: Double, color: Color, lineWidth: CGFloat = 10, backgroundColor: Color = Color(.systemGray5)) {
        self.progress = min(max(progress, 0), 1) // 确保进度在0-1之间
        self.color = color
        self.lineWidth = lineWidth
        self.backgroundColor = backgroundColor
    }
    
    var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(backgroundColor, lineWidth: lineWidth)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    color,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90)) // 从顶部开始
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
        }
        .onAppear {
            animatedProgress = progress
        }
        .onChange(of: progress) { newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newValue
            }
        }
    }
}

// MARK: - 带渐变的进度环
struct GradientProgressRingView: View {
    let progress: Double
    let colors: [Color]
    let lineWidth: CGFloat
    let backgroundColor: Color
    
    @State private var animatedProgress: Double = 0
    
    init(progress: Double, colors: [Color], lineWidth: CGFloat = 10, backgroundColor: Color = Color(.systemGray5)) {
        self.progress = min(max(progress, 0), 1)
        self.colors = colors
        self.lineWidth = lineWidth
        self.backgroundColor = backgroundColor
    }
    
    var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(backgroundColor, lineWidth: lineWidth)
            
            // 渐变进度圆环
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    AngularGradient(
                        gradient: Gradient(colors: colors),
                        center: .center,
                        startAngle: .degrees(-90),
                        endAngle: .degrees(-90 + 360 * animatedProgress)
                    ),
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
        }
        .onAppear {
            animatedProgress = progress
        }
        .onChange(of: progress) { newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newValue
            }
        }
    }
}

// MARK: - 多层进度环
struct MultiLayerProgressRingView: View {
    let layers: [ProgressLayer]
    let lineWidth: CGFloat
    
    struct ProgressLayer {
        let progress: Double
        let color: Color
        let label: String?
        
        init(progress: Double, color: Color, label: String? = nil) {
            self.progress = min(max(progress, 0), 1)
            self.color = color
            self.label = label
        }
    }
    
    var body: some View {
        ZStack {
            ForEach(Array(layers.enumerated()), id: \.offset) { index, layer in
                let ringLineWidth = lineWidth - CGFloat(index) * 2
                let ringRadius = 1.0 - Double(index) * 0.15
                
                ProgressRingView(
                    progress: layer.progress,
                    color: layer.color,
                    lineWidth: ringLineWidth
                )
                .scaleEffect(ringRadius)
            }
        }
    }
}

// MARK: - 带动画的脉冲进度环
struct PulsingProgressRingView: View {
    let progress: Double
    let color: Color
    let lineWidth: CGFloat
    
    @State private var animatedProgress: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            // 脉冲背景
            Circle()
                .stroke(color.opacity(0.3), lineWidth: lineWidth / 2)
                .scaleEffect(pulseScale)
                .opacity(2.0 - pulseScale)
                .animation(
                    Animation.easeInOut(duration: 1.5)
                        .repeatForever(autoreverses: false),
                    value: pulseScale
                )
            
            // 主进度环
            ProgressRingView(
                progress: animatedProgress,
                color: color,
                lineWidth: lineWidth
            )
        }
        .onAppear {
            animatedProgress = progress
            pulseScale = 1.3
        }
        .onChange(of: progress) { newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newValue
            }
        }
    }
}

// MARK: - 预览
#Preview("基础进度环") {
    VStack(spacing: 30) {
        ProgressRingView(progress: 0.75, color: .blue, lineWidth: 12)
            .frame(width: 100, height: 100)
            .overlay(
                Text("75%")
                    .font(.title2)
                    .fontWeight(.semibold)
            )
        
        ProgressRingView(progress: 0.45, color: .green, lineWidth: 8)
            .frame(width: 80, height: 80)
            .overlay(
                Text("45%")
                    .font(.headline)
                    .fontWeight(.medium)
            )
        
        ProgressRingView(progress: 0.90, color: .red, lineWidth: 10)
            .frame(width: 120, height: 120)
            .overlay(
                VStack {
                    Text("90%")
                        .font(.title)
                        .fontWeight(.bold)
                    Text("CPU")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            )
    }
    .padding()
}

#Preview("渐变进度环") {
    VStack(spacing: 30) {
        GradientProgressRingView(
            progress: 0.65,
            colors: [.blue, .purple],
            lineWidth: 12
        )
        .frame(width: 100, height: 100)
        .overlay(
            Text("65%")
                .font(.title2)
                .fontWeight(.semibold)
        )
        
        GradientProgressRingView(
            progress: 0.80,
            colors: [.green, .yellow, .red],
            lineWidth: 10
        )
        .frame(width: 120, height: 120)
        .overlay(
            VStack {
                Text("80%")
                    .font(.title)
                    .fontWeight(.bold)
                Text("内存")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        )
    }
    .padding()
}

#Preview("多层进度环") {
    MultiLayerProgressRingView(
        layers: [
            .init(progress: 0.75, color: .blue, label: "CPU"),
            .init(progress: 0.60, color: .green, label: "内存"),
            .init(progress: 0.45, color: .orange, label: "存储")
        ],
        lineWidth: 12
    )
    .frame(width: 150, height: 150)
    .overlay(
        VStack {
            Text("系统")
                .font(.headline)
                .fontWeight(.semibold)
            Text("状态")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    )
    .padding()
}

#Preview("脉冲进度环") {
    PulsingProgressRingView(
        progress: 0.85,
        color: .red,
        lineWidth: 10
    )
    .frame(width: 120, height: 120)
    .overlay(
        VStack {
            Text("85%")
                .font(.title)
                .fontWeight(.bold)
            Text("警告")
                .font(.caption)
                .foregroundColor(.red)
        }
    )
    .padding()
}
