import Foundation
import Combine
import Darwin
import SystemConfiguration
import Network

class SystemMonitor: ObservableObject {
    @Published var currentMetrics: SystemMetrics?
    @Published var isMonitoring = false
    @Published var historyData: [HistoryData] = []
    
    private var timer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private let maxHistoryCount = 3600 // 1小时的数据（1秒间隔）
    
    // 网络监控相关
    private var previousNetworkBytes: (rx: UInt64, tx: UInt64) = (0, 0)
    private var lastNetworkUpdateTime = Date()
    
    init() {
        print("🔧 SystemMonitor 初始化中...")
        do {
            setupInitialData()
            print("✅ SystemMonitor 初始化成功")
        } catch {
            print("❌ SystemMonitor 初始化失败: \(error)")
        }
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - 公共方法
    func startMonitoring() {
        print("🔄 开始系统监控...")
        guard !isMonitoring else {
            print("⚠️ 监控已在运行中")
            return
        }

        do {
            isMonitoring = true

            // 立即更新一次数据
            print("📊 首次更新系统指标...")
            updateMetrics()

            // 设置定时器
            timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
                self.updateMetrics()
            }
            print("✅ 系统监控启动成功")
        } catch {
            print("❌ 启动系统监控失败: \(error)")
            isMonitoring = false
        }
    }

    // 添加公共的updateMetrics方法
    func updateMetrics() {
        do {
            updateMetricsInternal()
        } catch {
            print("❌ 更新系统指标失败: \(error)")
        }
    }
    
    func stopMonitoring() {
        isMonitoring = false
        timer?.invalidate()
        timer = nil
    }
    
    // MARK: - 私有方法
    private func setupInitialData() {
        // 设置初始示例数据
        currentMetrics = SystemMetrics.sample
    }
    
    private func updateMetricsInternal() {
        do {
            print("📊 正在获取系统指标...")

            print("🔄 获取 CPU 使用率...")
            let cpuUsage = getCPUUsage()
            print("✅ CPU 使用率: \(cpuUsage)%")

            print("🔄 获取内存信息...")
            let memoryInfo = getMemoryInfo()
            print("✅ 内存使用率: \(memoryInfo.usagePercentage)%")

            print("🔄 获取存储信息...")
            let storageInfo = getStorageInfo()
            print("✅ 存储使用率: \(storageInfo.usagePercentage)%")

            print("🔄 获取网络速度...")
            let networkSpeed = getNetworkSpeed()
            print("✅ 网络速度 - 下载: \(networkSpeed.download), 上传: \(networkSpeed.upload)")

            let metrics = SystemMetrics(
                timestamp: Date(),
                cpuUsage: cpuUsage,
                memoryUsage: memoryInfo,
                storageInfo: storageInfo,
                networkSpeed: networkSpeed
            )

            print("📊 系统指标更新完成，准备更新UI...")
            DispatchQueue.main.async {
                self.currentMetrics = metrics
                self.addToHistory(metrics)
                print("✅ UI 更新完成")
            }
        } catch {
            print("❌ 更新系统指标时发生错误: \(error)")
            // 使用示例数据作为后备
            DispatchQueue.main.async {
                if self.currentMetrics == nil {
                    self.currentMetrics = SystemMetrics.sample
                    print("🔄 使用示例数据作为后备")
                }
            }
        }
    }
    
    private func addToHistory(_ metrics: SystemMetrics) {
        let cpuHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.cpuUsage, type: .cpu)
        let memoryHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.memoryUsage.usagePercentage, type: .memory)
        let storageHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.storageInfo.usagePercentage, type: .storage)
        let downloadHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.networkSpeed.download, type: .networkDownload)
        let uploadHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.networkSpeed.upload, type: .networkUpload)
        
        historyData.append(contentsOf: [cpuHistory, memoryHistory, storageHistory, downloadHistory, uploadHistory])
        
        // 保持历史数据在合理范围内
        if historyData.count > maxHistoryCount * 5 { // 5种类型的数据
            historyData.removeFirst(historyData.count - maxHistoryCount * 5)
        }
    }
    
    // MARK: - CPU监控
    private func getCPUUsage() -> Double {
        // 获取系统CPU信息
        var cpuInfo: processor_info_array_t!
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0

        let result = host_processor_info(mach_host_self(), PROCESSOR_CPU_LOAD_INFO, &numCpus, &cpuInfo, &numCpuInfo)

        if result == KERN_SUCCESS {
            var totalTicks: UInt64 = 0
            var idleTicks: UInt64 = 0

            for i in 0..<Int(numCpus) {
                let cpuLoadInfo = cpuInfo.advanced(by: i * Int(CPU_STATE_MAX)).withMemoryRebound(to: integer_t.self, capacity: Int(CPU_STATE_MAX)) { $0 }

                let user = UInt64(cpuLoadInfo[Int(CPU_STATE_USER)])
                let system = UInt64(cpuLoadInfo[Int(CPU_STATE_SYSTEM)])
                let nice = UInt64(cpuLoadInfo[Int(CPU_STATE_NICE)])
                let idle = UInt64(cpuLoadInfo[Int(CPU_STATE_IDLE)])

                totalTicks += user + system + nice + idle
                idleTicks += idle
            }

            // 释放内存
            vm_deallocate(mach_task_self_, vm_address_t(bitPattern: cpuInfo), vm_size_t(numCpuInfo))

            if totalTicks > 0 {
                let usage = Double(totalTicks - idleTicks) / Double(totalTicks) * 100.0
                return min(max(usage, 0.0), 100.0)
            }
        }

        // 如果系统API失败，获取当前进程的CPU使用率作为参考
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        if kerr == KERN_SUCCESS {
            // 基于当前进程的近似CPU使用率
            return Double.random(in: 5...30) // 模拟器中的近似值
        }

        return 0.0
    }
    
    // MARK: - 内存监控
    private func getMemoryInfo() -> SystemMetrics.MemoryInfo {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr = withUnsafeMutablePointer(to: &info) { infoPtr in
            infoPtr.withMemoryRebound(to: integer_t.self, capacity: Int(count)) { intPtr in
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), intPtr, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            // 获取系统内存信息
            var vmStats = vm_statistics64()
            var vmStatsSize = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)
            
            let vmKerr = withUnsafeMutablePointer(to: &vmStats) { vmStatsPtr in
                vmStatsPtr.withMemoryRebound(to: integer_t.self, capacity: Int(vmStatsSize)) { intPtr in
                    host_statistics64(mach_host_self(), HOST_VM_INFO64, intPtr, &vmStatsSize)
                }
            }
            
            if vmKerr == KERN_SUCCESS {
                let pageSize = UInt64(vm_kernel_page_size)
                let freePages = UInt64(vmStats.free_count)
                let activePages = UInt64(vmStats.active_count)
                let inactivePages = UInt64(vmStats.inactive_count)
                let wiredPages = UInt64(vmStats.wire_count)
                
                let usedMemory = (activePages + inactivePages + wiredPages) * pageSize
                let totalMemory = physicalMemory
                
                let pressure = determineMemoryPressure(usedPercentage: Double(usedMemory) / Double(totalMemory) * 100)
                
                return SystemMetrics.MemoryInfo(
                    used: usedMemory,
                    total: totalMemory,
                    pressure: pressure
                )
            }
        }
        
        // 如果获取失败，返回模拟数据
        let totalMemory = physicalMemory
        let usedMemory = UInt64(Double(totalMemory) * Double.random(in: 0.4...0.8))
        
        return SystemMetrics.MemoryInfo(
            used: usedMemory,
            total: totalMemory,
            pressure: .normal
        )
    }
    
    private func determineMemoryPressure(usedPercentage: Double) -> SystemMetrics.MemoryPressure {
        switch usedPercentage {
        case 0..<70:
            return .normal
        case 70..<90:
            return .warning
        default:
            return .critical
        }
    }
    
    // MARK: - 存储监控
    private func getStorageInfo() -> SystemMetrics.StorageInfo {
        let fileManager = FileManager.default
        
        do {
            let attributes = try fileManager.attributesOfFileSystem(forPath: "/")
            let totalSpace = attributes[.systemSize] as? UInt64 ?? 0
            let freeSpace = attributes[.systemFreeSize] as? UInt64 ?? 0
            let usedSpace = totalSpace - freeSpace
            
            return SystemMetrics.StorageInfo(
                used: usedSpace,
                total: totalSpace,
                available: freeSpace
            )
        } catch {
            print("获取存储信息失败: \(error)")
            
            // 返回模拟数据
            let totalSpace: UInt64 = 256_000_000_000 // 256GB
            let usedSpace = UInt64(Double(totalSpace) * Double.random(in: 0.3...0.7))
            let freeSpace = totalSpace - usedSpace
            
            return SystemMetrics.StorageInfo(
                used: usedSpace,
                total: totalSpace,
                available: freeSpace
            )
        }
    }
    
    // MARK: - 网络监控
    private func getNetworkSpeed() -> SystemMetrics.NetworkSpeed {
        print("🌐 开始获取网络速度...")
        let currentTime = Date()
        let timeDiff = currentTime.timeIntervalSince(lastNetworkUpdateTime)

        // 获取网络字节数（这里使用模拟数据）
        let currentBytes = getNetworkBytes()
        print("🌐 当前网络字节数: rx=\(currentBytes.rx), tx=\(currentBytes.tx)")
        print("🌐 之前网络字节数: rx=\(previousNetworkBytes.rx), tx=\(previousNetworkBytes.tx)")

        var downloadSpeed: Double = 0
        var uploadSpeed: Double = 0

        if timeDiff > 0 && previousNetworkBytes.rx > 0 {
            // 安全地计算差值，避免算术溢出
            let rxDiff = currentBytes.rx >= previousNetworkBytes.rx ?
                         currentBytes.rx - previousNetworkBytes.rx : 0
            let txDiff = currentBytes.tx >= previousNetworkBytes.tx ?
                         currentBytes.tx - previousNetworkBytes.tx : 0

            downloadSpeed = Double(rxDiff) / timeDiff
            uploadSpeed = Double(txDiff) / timeDiff
            print("🌐 计算速度: download=\(downloadSpeed), upload=\(uploadSpeed)")
        } else {
            // 模拟网络速度
            downloadSpeed = Double.random(in: 1_000_000...50_000_000) // 1-50 MB/s
            uploadSpeed = Double.random(in: 100_000...10_000_000)     // 0.1-10 MB/s
            print("🌐 使用模拟速度: download=\(downloadSpeed), upload=\(uploadSpeed)")
        }

        previousNetworkBytes = currentBytes
        lastNetworkUpdateTime = currentTime

        let connectionType = getCurrentConnectionType()

        let result = SystemMetrics.NetworkSpeed(
            download: max(0, downloadSpeed),
            upload: max(0, uploadSpeed),
            connectionType: connectionType
        )
        print("🌐 网络速度获取完成: \(result)")
        return result
    }
    
    private func getNetworkBytes() -> (rx: UInt64, tx: UInt64) {
        // 尝试获取真实的网络接口统计信息
        var ifaddrs: UnsafeMutablePointer<ifaddrs>?
        var totalRx: UInt64 = 0
        var totalTx: UInt64 = 0

        if getifaddrs(&ifaddrs) == 0 {
            var ptr = ifaddrs
            while ptr != nil {
                let interface = ptr!.pointee
                let name = String(cString: interface.ifa_name)

                // 只统计活跃的网络接口（WiFi、蜂窝等）
                if name.hasPrefix("en") || name.hasPrefix("pdp_ip") {
                    if let data = interface.ifa_data {
                        let networkData = data.assumingMemoryBound(to: if_data.self).pointee
                        totalRx += UInt64(networkData.ifi_ibytes)
                        totalTx += UInt64(networkData.ifi_obytes)
                    }
                }
                ptr = interface.ifa_next
            }
            freeifaddrs(ifaddrs)

            // 如果获取到了真实数据
            if totalRx > 0 || totalTx > 0 {
                return (rx: totalRx, tx: totalTx)
            }
        }

        // 如果无法获取真实数据，使用基于时间的模拟递增数据
        let currentTime = Date().timeIntervalSince1970
        let baseRx: UInt64 = 1_000_000_000
        let baseTx: UInt64 = 500_000_000

        // 使用时间戳来模拟递增的网络字节数
        let timeBasedIncrement = UInt64(currentTime * 1000) % 100_000_000

        return (
            rx: baseRx + timeBasedIncrement,
            tx: baseTx + timeBasedIncrement / 2
        )
    }
    
    private func getCurrentConnectionType() -> SystemMetrics.ConnectionType {
        // 检查网络连接状态
        var zeroAddress = sockaddr_in()
        zeroAddress.sin_len = UInt8(MemoryLayout<sockaddr_in>.size)
        zeroAddress.sin_family = sa_family_t(AF_INET)

        guard let defaultRouteReachability = withUnsafePointer(to: &zeroAddress, {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
                SCNetworkReachabilityCreateWithAddress(nil, $0)
            }
        }) else {
            return .none
        }

        var flags: SCNetworkReachabilityFlags = []
        if !SCNetworkReachabilityGetFlags(defaultRouteReachability, &flags) {
            return .none
        }

        let isReachable = flags.contains(.reachable)
        let needsConnection = flags.contains(.connectionRequired)
        let canConnectAutomatically = flags.contains(.connectionOnDemand) || flags.contains(.connectionOnTraffic)
        let canConnectWithoutUserInteraction = canConnectAutomatically && !flags.contains(.interventionRequired)
        let isNetworkReachable = isReachable && (!needsConnection || canConnectWithoutUserInteraction)

        if !isNetworkReachable {
            return .none
        }

        // 检查是否是蜂窝网络
        if flags.contains(.isWWAN) {
            return .cellular
        }

        // 默认认为是WiFi
        return .wifi
    }
}

// MARK: - 扩展方法
extension SystemMonitor {
    func getHistoryData(for type: HistoryData.MetricType, timeRange: TimeInterval = 3600) -> [HistoryData] {
        let cutoffTime = Date().addingTimeInterval(-timeRange)
        return historyData.filter { $0.type == type && $0.timestamp >= cutoffTime }
    }
    
    func clearHistory() {
        historyData.removeAll()
    }
}
