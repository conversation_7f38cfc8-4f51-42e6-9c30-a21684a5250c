import Foundation
import Combine
import Darwin

class SystemMonitor: ObservableObject {
    @Published var currentMetrics: SystemMetrics?
    @Published var isMonitoring = false
    @Published var historyData: [HistoryData] = []
    
    private var timer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private let maxHistoryCount = 3600 // 1小时的数据（1秒间隔）
    
    // 网络监控相关
    private var previousNetworkBytes: (rx: UInt64, tx: UInt64) = (0, 0)
    private var lastNetworkUpdateTime = Date()
    
    init() {
        setupInitialData()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - 公共方法
    func startMonitoring() {
        guard !isMonitoring else { return }

        isMonitoring = true

        // 立即更新一次数据
        updateMetrics()

        // 设置定时器
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.updateMetrics()
        }
    }

    // 添加公共的updateMetrics方法
    func updateMetrics() {
        updateMetricsInternal()
    }
    
    func stopMonitoring() {
        isMonitoring = false
        timer?.invalidate()
        timer = nil
    }
    
    // MARK: - 私有方法
    private func setupInitialData() {
        // 设置初始示例数据
        currentMetrics = SystemMetrics.sample
    }
    
    private func updateMetricsInternal() {
        let cpuUsage = getCPUUsage()
        let memoryInfo = getMemoryInfo()
        let storageInfo = getStorageInfo()
        let networkSpeed = getNetworkSpeed()
        
        let metrics = SystemMetrics(
            timestamp: Date(),
            cpuUsage: cpuUsage,
            memoryUsage: memoryInfo,
            storageInfo: storageInfo,
            networkSpeed: networkSpeed
        )
        
        DispatchQueue.main.async {
            self.currentMetrics = metrics
            self.addToHistory(metrics)
        }
    }
    
    private func addToHistory(_ metrics: SystemMetrics) {
        let cpuHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.cpuUsage, type: .cpu)
        let memoryHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.memoryUsage.usagePercentage, type: .memory)
        let storageHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.storageInfo.usagePercentage, type: .storage)
        let downloadHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.networkSpeed.download, type: .networkDownload)
        let uploadHistory = HistoryData(timestamp: metrics.timestamp, value: metrics.networkSpeed.upload, type: .networkUpload)
        
        historyData.append(contentsOf: [cpuHistory, memoryHistory, storageHistory, downloadHistory, uploadHistory])
        
        // 保持历史数据在合理范围内
        if historyData.count > maxHistoryCount * 5 { // 5种类型的数据
            historyData.removeFirst(historyData.count - maxHistoryCount * 5)
        }
    }
    
    // MARK: - CPU监控
    private func getCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            // 这里是一个简化的CPU使用率计算
            // 实际应用中需要更复杂的计算来获取系统整体CPU使用率
            return Double.random(in: 20...80) // 模拟数据
        }
        
        return 0.0
    }
    
    // MARK: - 内存监控
    private func getMemoryInfo() -> SystemMetrics.MemoryInfo {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr = withUnsafeMutablePointer(to: &info) { infoPtr in
            infoPtr.withMemoryRebound(to: integer_t.self, capacity: Int(count)) { intPtr in
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), intPtr, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            // 获取系统内存信息
            var vmStats = vm_statistics64()
            var vmStatsSize = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)
            
            let vmKerr = withUnsafeMutablePointer(to: &vmStats) { vmStatsPtr in
                vmStatsPtr.withMemoryRebound(to: integer_t.self, capacity: Int(vmStatsSize)) { intPtr in
                    host_statistics64(mach_host_self(), HOST_VM_INFO64, intPtr, &vmStatsSize)
                }
            }
            
            if vmKerr == KERN_SUCCESS {
                let pageSize = UInt64(vm_kernel_page_size)
                let freePages = UInt64(vmStats.free_count)
                let activePages = UInt64(vmStats.active_count)
                let inactivePages = UInt64(vmStats.inactive_count)
                let wiredPages = UInt64(vmStats.wire_count)
                
                let usedMemory = (activePages + inactivePages + wiredPages) * pageSize
                let totalMemory = physicalMemory
                
                let pressure = determineMemoryPressure(usedPercentage: Double(usedMemory) / Double(totalMemory) * 100)
                
                return SystemMetrics.MemoryInfo(
                    used: usedMemory,
                    total: totalMemory,
                    pressure: pressure
                )
            }
        }
        
        // 如果获取失败，返回模拟数据
        let totalMemory = physicalMemory
        let usedMemory = UInt64(Double(totalMemory) * Double.random(in: 0.4...0.8))
        
        return SystemMetrics.MemoryInfo(
            used: usedMemory,
            total: totalMemory,
            pressure: .normal
        )
    }
    
    private func determineMemoryPressure(usedPercentage: Double) -> SystemMetrics.MemoryPressure {
        switch usedPercentage {
        case 0..<70:
            return .normal
        case 70..<90:
            return .warning
        default:
            return .critical
        }
    }
    
    // MARK: - 存储监控
    private func getStorageInfo() -> SystemMetrics.StorageInfo {
        let fileManager = FileManager.default
        
        do {
            let attributes = try fileManager.attributesOfFileSystem(forPath: "/")
            let totalSpace = attributes[.systemSize] as? UInt64 ?? 0
            let freeSpace = attributes[.systemFreeSize] as? UInt64 ?? 0
            let usedSpace = totalSpace - freeSpace
            
            return SystemMetrics.StorageInfo(
                used: usedSpace,
                total: totalSpace,
                available: freeSpace
            )
        } catch {
            print("获取存储信息失败: \(error)")
            
            // 返回模拟数据
            let totalSpace: UInt64 = 256_000_000_000 // 256GB
            let usedSpace = UInt64(Double(totalSpace) * Double.random(in: 0.3...0.7))
            let freeSpace = totalSpace - usedSpace
            
            return SystemMetrics.StorageInfo(
                used: usedSpace,
                total: totalSpace,
                available: freeSpace
            )
        }
    }
    
    // MARK: - 网络监控
    private func getNetworkSpeed() -> SystemMetrics.NetworkSpeed {
        let currentTime = Date()
        let timeDiff = currentTime.timeIntervalSince(lastNetworkUpdateTime)
        
        // 获取网络字节数（这里使用模拟数据）
        let currentBytes = getNetworkBytes()
        
        var downloadSpeed: Double = 0
        var uploadSpeed: Double = 0
        
        if timeDiff > 0 && previousNetworkBytes.rx > 0 {
            downloadSpeed = Double(currentBytes.rx - previousNetworkBytes.rx) / timeDiff
            uploadSpeed = Double(currentBytes.tx - previousNetworkBytes.tx) / timeDiff
        } else {
            // 模拟网络速度
            downloadSpeed = Double.random(in: 1_000_000...50_000_000) // 1-50 MB/s
            uploadSpeed = Double.random(in: 100_000...10_000_000)     // 0.1-10 MB/s
        }
        
        previousNetworkBytes = currentBytes
        lastNetworkUpdateTime = currentTime
        
        let connectionType = getCurrentConnectionType()
        
        return SystemMetrics.NetworkSpeed(
            download: max(0, downloadSpeed),
            upload: max(0, uploadSpeed),
            connectionType: connectionType
        )
    }
    
    private func getNetworkBytes() -> (rx: UInt64, tx: UInt64) {
        // 这里应该实现真实的网络字节数获取
        // 由于iOS的限制，我们使用模拟数据
        let baseRx: UInt64 = 1_000_000_000
        let baseTx: UInt64 = 500_000_000
        
        return (
            rx: baseRx + UInt64.random(in: 0...100_000_000),
            tx: baseTx + UInt64.random(in: 0...50_000_000)
        )
    }
    
    private func getCurrentConnectionType() -> SystemMetrics.ConnectionType {
        // 这里应该实现真实的网络连接类型检测
        // 由于演示目的，我们返回WiFi
        return .wifi
    }
}

// MARK: - 扩展方法
extension SystemMonitor {
    func getHistoryData(for type: HistoryData.MetricType, timeRange: TimeInterval = 3600) -> [HistoryData] {
        let cutoffTime = Date().addingTimeInterval(-timeRange)
        return historyData.filter { $0.type == type && $0.timestamp >= cutoffTime }
    }
    
    func clearHistory() {
        historyData.removeAll()
    }
}
