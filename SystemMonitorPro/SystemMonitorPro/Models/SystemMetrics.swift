import Foundation

struct SystemMetrics: Codable {
    let timestamp: Date
    let cpuUsage: Double
    let memoryUsage: MemoryInfo
    let storageInfo: StorageInfo
    let networkSpeed: NetworkSpeed
    
    struct MemoryInfo: Codable {
        let used: UInt64
        let total: UInt64
        let pressure: MemoryPressure
        
        var usagePercentage: Double {
            guard total > 0 else { return 0 }
            return Double(used) / Double(total) * 100
        }
        
        var available: UInt64 {
            return total - used
        }
    }
    
    struct StorageInfo: Codable {
        let used: UInt64
        let total: UInt64
        let available: UInt64
        
        var usagePercentage: Double {
            guard total > 0 else { return 0 }
            return Double(used) / Double(total) * 100
        }
    }
    
    struct NetworkSpeed: Codable {
        let download: Double // bytes per second
        let upload: Double   // bytes per second
        let connectionType: ConnectionType
        
        var totalSpeed: Double {
            return download + upload
        }
    }
    
    enum MemoryPressure: String, Codable, CaseIterable {
        case normal = "Normal"
        case warning = "Warning"
        case critical = "Critical"
        
        var color: String {
            switch self {
            case .normal:
                return "green"
            case .warning:
                return "orange"
            case .critical:
                return "red"
            }
        }
    }
    
    enum ConnectionType: String, Codable, CaseIterable {
        case wifi = "WiFi"
        case cellular = "蜂窝网络"
        case ethernet = "以太网"
        case none = "无连接"
        
        var systemImageName: String {
            switch self {
            case .wifi:
                return "wifi"
            case .cellular:
                return "antenna.radiowaves.left.and.right"
            case .ethernet:
                return "cable.connector"
            case .none:
                return "wifi.slash"
            }
        }
    }
}

// MARK: - 扩展方法
extension SystemMetrics {
    static var sample: SystemMetrics {
        return SystemMetrics(
            timestamp: Date(),
            cpuUsage: 45.6,
            memoryUsage: MemoryInfo(
                used: 4_500_000_000, // 4.5GB
                total: 8_000_000_000, // 8GB
                pressure: .normal
            ),
            storageInfo: StorageInfo(
                used: 128_000_000_000, // 128GB
                total: 256_000_000_000, // 256GB
                available: 128_000_000_000 // 128GB
            ),
            networkSpeed: NetworkSpeed(
                download: 12_500_000, // 12.5 MB/s
                upload: 2_100_000,    // 2.1 MB/s
                connectionType: .wifi
            )
        )
    }
    
    var overallHealthStatus: HealthStatus {
        let cpuHealth = cpuUsage < 70 ? HealthStatus.good : (cpuUsage < 90 ? HealthStatus.warning : HealthStatus.critical)
        let memoryHealth = memoryUsage.usagePercentage < 80 ? HealthStatus.good : (memoryUsage.usagePercentage < 95 ? HealthStatus.warning : HealthStatus.critical)
        let storageHealth = storageInfo.usagePercentage < 85 ? HealthStatus.good : (storageInfo.usagePercentage < 95 ? HealthStatus.warning : HealthStatus.critical)
        
        let statuses = [cpuHealth, memoryHealth, storageHealth]
        
        if statuses.contains(.critical) {
            return .critical
        } else if statuses.contains(.warning) {
            return .warning
        } else {
            return .good
        }
    }
}

enum HealthStatus: String, CaseIterable {
    case good = "良好"
    case warning = "警告"
    case critical = "严重"
    
    var color: String {
        switch self {
        case .good:
            return "green"
        case .warning:
            return "orange"
        case .critical:
            return "red"
        }
    }
    
    var systemImageName: String {
        switch self {
        case .good:
            return "checkmark.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .critical:
            return "xmark.circle.fill"
        }
    }
}

// MARK: - 历史数据模型
struct HistoryData: Codable, Identifiable {
    let id = UUID()
    let timestamp: Date
    let value: Double
    let type: MetricType
    
    enum MetricType: String, Codable, CaseIterable {
        case cpu = "CPU"
        case memory = "内存"
        case storage = "存储"
        case networkDownload = "下载速度"
        case networkUpload = "上传速度"
    }
}

// MARK: - 用户偏好设置
struct UserPreferences: Codable {
    var refreshInterval: TimeInterval = 1.0
    var enableNotifications: Bool = true
    var notificationThresholds: NotificationThresholds = NotificationThresholds()
    var appearanceMode: AppearanceMode = .system
    
    struct NotificationThresholds: Codable {
        var cpuThreshold: Double = 80.0
        var memoryThreshold: Double = 85.0
        var storageThreshold: Double = 90.0
    }
    
    enum AppearanceMode: String, Codable, CaseIterable {
        case light = "浅色"
        case dark = "深色"
        case system = "跟随系统"
    }
}
