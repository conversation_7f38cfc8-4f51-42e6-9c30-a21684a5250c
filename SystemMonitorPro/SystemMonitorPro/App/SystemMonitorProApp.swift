import SwiftUI
import os.log

@main
struct SystemMonitorProApp: App {
    @StateObject private var systemMonitor = SystemMonitor()

    init() {
        // 设置详细日志
        print("🚀 SystemMonitorPro App 启动中...")

        // 捕获未处理的异常
        NSSetUncaughtExceptionHandler { exception in
            print("💥 未捕获的异常: \(exception)")
            print("💥 异常原因: \(exception.reason ?? "未知")")
            print("💥 调用栈: \(exception.callStackSymbols)")
        }

        // 设置信号处理
        signal(SIGABRT) { signal in
            print("💥 收到 SIGABRT 信号: \(signal)")
        }

        signal(SIGSEGV) { signal in
            print("💥 收到 SIGSEGV 信号: \(signal)")
        }
    }

    var body: some Scene {
        WindowGroup {
            Group {
                do {
                    print("📱 正在创建 ContentView...")
                    return AnyView(
                        ContentView()
                            .environmentObject(systemMonitor)
                            .onAppear {
                                print("📱 ContentView 出现，开始监控...")
                                do {
                                    systemMonitor.startMonitoring()
                                    print("✅ 系统监控启动成功")
                                } catch {
                                    print("❌ 启动系统监控失败: \(error)")
                                }
                            }
                            .onDisappear {
                                print("📱 ContentView 消失，停止监控...")
                                systemMonitor.stopMonitoring()
                            }
                    )
                } catch {
                    print("❌ 创建 ContentView 失败: \(error)")
                    return AnyView(
                        VStack {
                            Text("应用启动失败")
                                .font(.title)
                                .foregroundColor(.red)
                            Text("错误: \(error.localizedDescription)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    )
                }
            }
        }
    }
}
