import SwiftUI

@main
struct SystemMonitorProApp: App {
    @StateObject private var systemMonitor = SystemMonitor()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(systemMonitor)
                .onAppear {
                    systemMonitor.startMonitoring()
                }
                .onDisappear {
                    systemMonitor.stopMonitoring()
                }
        }
    }
}
