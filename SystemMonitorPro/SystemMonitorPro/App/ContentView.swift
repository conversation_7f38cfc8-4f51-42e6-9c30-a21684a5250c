import SwiftUI

struct ContentView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            DashboardView()
                .tabItem {
                    Image(systemName: "gauge")
                    Text("仪表盘")
                }
                .tag(0)
            
            CPUDetailView()
                .tabItem {
                    Image(systemName: "cpu")
                    Text("CPU")
                }
                .tag(1)
            
            MemoryDetailView()
                .tabItem {
                    Image(systemName: "memorychip")
                    Text("内存")
                }
                .tag(2)
            
            StorageDetailView()
                .tabItem {
                    Image(systemName: "internaldrive")
                    Text("存储")
                }
                .tag(3)
            
            NetworkDetailView()
                .tabItem {
                    Image(systemName: "network")
                    Text("网络")
                }
                .tag(4)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("设置")
                }
                .tag(5)
        }
        .accentColor(.blue)
    }
}

// 临时占位视图，稍后会实现具体功能
struct CPUDetailView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    
    var body: some View {
        NavigationView {
            VStack {
                Text("CPU详情")
                    .font(.largeTitle)
                    .padding()
                
                if let metrics = systemMonitor.currentMetrics {
                    VStack(spacing: 20) {
                        ZStack {
                            Circle()
                                .stroke(cpuColor(for: metrics.cpuUsage).opacity(0.3), lineWidth: 12)
                                .frame(width: 150, height: 150)

                            Circle()
                                .trim(from: 0, to: CGFloat(metrics.cpuUsage / 100))
                                .stroke(cpuColor(for: metrics.cpuUsage), style: StrokeStyle(lineWidth: 12, lineCap: .round))
                                .rotationEffect(.degrees(-90))
                                .frame(width: 150, height: 150)
                                .animation(.easeInOut(duration: 0.5), value: metrics.cpuUsage)
                        }
                        .overlay(
                            Text(String(format: "%.1f%%", metrics.cpuUsage))
                                .font(.title2)
                                .fontWeight(.semibold)
                        )
                        
                        Text("CPU使用率")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                } else {
                    Text("正在加载...")
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .navigationTitle("CPU监控")
        }
    }
    
    private func cpuColor(for usage: Double) -> Color {
        switch usage {
        case 0..<50:
            return .green
        case 50..<80:
            return .orange
        default:
            return .red
        }
    }
}

struct MemoryDetailView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    
    var body: some View {
        NavigationView {
            VStack {
                Text("内存详情")
                    .font(.largeTitle)
                    .padding()
                
                if let metrics = systemMonitor.currentMetrics {
                    VStack(spacing: 20) {
                        ZStack {
                            Circle()
                                .stroke(memoryColor(for: metrics.memoryUsage.usagePercentage).opacity(0.3), lineWidth: 12)
                                .frame(width: 150, height: 150)

                            Circle()
                                .trim(from: 0, to: CGFloat(metrics.memoryUsage.usagePercentage / 100))
                                .stroke(memoryColor(for: metrics.memoryUsage.usagePercentage), style: StrokeStyle(lineWidth: 12, lineCap: .round))
                                .rotationEffect(.degrees(-90))
                                .frame(width: 150, height: 150)
                                .animation(.easeInOut(duration: 0.5), value: metrics.memoryUsage.usagePercentage)
                        }
                        .overlay(
                            VStack {
                                Text("\(metrics.memoryUsage.usagePercentage, specifier: "%.1f")%")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Text("\(formatBytes(metrics.memoryUsage.used))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        )
                        
                        VStack(spacing: 8) {
                            Text("内存使用情况")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Text("已用: \(formatBytes(metrics.memoryUsage.used))")
                                Spacer()
                                Text("总计: \(formatBytes(metrics.memoryUsage.total))")
                            }
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)
                    }
                } else {
                    Text("正在加载...")
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .navigationTitle("内存监控")
        }
    }
    
    private func memoryColor(for usage: Double) -> Color {
        switch usage {
        case 0..<60:
            return .green
        case 60..<85:
            return .orange
        default:
            return .red
        }
    }
}

struct StorageDetailView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    
    var body: some View {
        NavigationView {
            VStack {
                Text("存储详情")
                    .font(.largeTitle)
                    .padding()
                
                if let metrics = systemMonitor.currentMetrics {
                    VStack(spacing: 20) {
                        ZStack {
                            Circle()
                                .stroke(storageColor(for: metrics.storageInfo.usagePercentage).opacity(0.3), lineWidth: 12)
                                .frame(width: 150, height: 150)

                            Circle()
                                .trim(from: 0, to: CGFloat(metrics.storageInfo.usagePercentage / 100))
                                .stroke(storageColor(for: metrics.storageInfo.usagePercentage), style: StrokeStyle(lineWidth: 12, lineCap: .round))
                                .rotationEffect(.degrees(-90))
                                .frame(width: 150, height: 150)
                                .animation(.easeInOut(duration: 0.5), value: metrics.storageInfo.usagePercentage)
                        }
                        .overlay(
                            VStack {
                                Text(String(format: "%.1f%%", metrics.storageInfo.usagePercentage))
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                Text("\(formatBytes(metrics.storageInfo.used))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        )
                        
                        VStack(spacing: 8) {
                            Text("存储使用情况")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            VStack(spacing: 4) {
                                HStack {
                                    Text("已用: \(formatBytes(metrics.storageInfo.used))")
                                    Spacer()
                                    Text("总计: \(formatBytes(metrics.storageInfo.total))")
                                }
                                HStack {
                                    Text("可用: \(formatBytes(metrics.storageInfo.available))")
                                    Spacer()
                                }
                            }
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)
                    }
                } else {
                    Text("正在加载...")
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .navigationTitle("存储监控")
        }
    }
    
    private func storageColor(for usage: Double) -> Color {
        switch usage {
        case 0..<70:
            return .green
        case 70..<90:
            return .orange
        default:
            return .red
        }
    }
}

struct NetworkDetailView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor
    
    var body: some View {
        NavigationView {
            VStack {
                Text("网络详情")
                    .font(.largeTitle)
                    .padding()
                
                if let metrics = systemMonitor.currentMetrics {
                    VStack(spacing: 30) {
                        HStack(spacing: 40) {
                            VStack {
                                Image(systemName: "arrow.down.circle.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.blue)
                                Text("下载")
                                    .font(.headline)
                                Text("\(formatSpeed(metrics.networkSpeed.download))")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                            }
                            
                            VStack {
                                Image(systemName: "arrow.up.circle.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.green)
                                Text("上传")
                                    .font(.headline)
                                Text("\(formatSpeed(metrics.networkSpeed.upload))")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                            }
                        }
                        
                        VStack(spacing: 8) {
                            Text("连接类型")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            Text(metrics.networkSpeed.connectionType.rawValue)
                                .font(.title3)
                                .fontWeight(.medium)
                        }
                    }
                } else {
                    Text("正在加载...")
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .navigationTitle("网络监控")
        }
    }
}

struct SettingsView: View {
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("应用设置")) {
                    HStack {
                        Image(systemName: "bell")
                        Text("通知设置")
                    }
                    
                    HStack {
                        Image(systemName: "paintbrush")
                        Text("外观设置")
                    }
                    
                    HStack {
                        Image(systemName: "clock")
                        Text("刷新频率")
                    }
                }
                
                Section(header: Text("关于")) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text("应用信息")
                    }
                    
                    HStack {
                        Image(systemName: "star")
                        Text("评价应用")
                    }
                }
            }
            .navigationTitle("设置")
        }
    }
}

// 辅助函数
private func formatBytes(_ bytes: UInt64) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useGB, .useMB]
    formatter.countStyle = .memory
    return formatter.string(fromByteCount: Int64(bytes))
}

private func formatSpeed(_ bytesPerSecond: Double) -> String {
    let formatter = ByteCountFormatter()
    formatter.allowedUnits = [.useMB, .useKB]
    formatter.countStyle = .memory
    return formatter.string(fromByteCount: Int64(bytesPerSecond)) + "/s"
}

#Preview {
    ContentView()
        .environmentObject(SystemMonitor())
}
