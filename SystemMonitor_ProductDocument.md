# iOS系统监控小组件应用产品文档

## 1. 产品概述

### 1.1 产品名称
**SystemMonitor Pro** - iOS系统性能监控小组件

### 1.2 产品定位
一款专业的iOS系统性能监控应用，提供实时的CPU、内存、存储空间和网络速度监控功能，支持桌面小组件、菜单栏常驻和主应用界面多种展示形式。

### 1.3 目标用户
- iOS开发者和技术爱好者
- 需要监控设备性能的专业用户
- 关注设备运行状态的普通用户

### 1.4 核心价值
- 实时监控设备性能指标
- 多样化的展示方式（小组件、菜单栏、主应用）
- 简洁直观的用户界面
- 低功耗的后台监控

## 2. 技术架构

### 2.1 开发语言和框架
- **主要语言**: Swift 5.9+
- **UI框架**: SwiftUI + UIKit（兼容性考虑）
- **最低支持版本**: iOS 14.0+
- **开发工具**: Xcode 15+

### 2.2 架构设计
```
┌─────────────────────────────────────┐
│           主应用 (Main App)          │
├─────────────────────────────────────┤
│        小组件扩展 (Widget Extension)  │
├─────────────────────────────────────┤
│      菜单栏扩展 (Menu Bar Extension)  │
├─────────────────────────────────────┤
│        共享数据层 (Shared Data)      │
├─────────────────────────────────────┤
│        系统API层 (System APIs)       │
└─────────────────────────────────────┘
```

### 2.3 核心模块
1. **数据采集模块** (DataCollector)
2. **数据存储模块** (DataStorage)
3. **UI展示模块** (UIComponents)
4. **小组件模块** (WidgetKit)
5. **菜单栏模块** (MenuBarExtra)
6. **设置管理模块** (SettingsManager)

## 3. 功能规格

### 3.1 核心监控功能

#### 3.1.1 CPU监控
- **指标**: CPU使用率（总体和各核心）
- **更新频率**: 1秒
- **展示方式**: 百分比 + 环形进度条
- **历史数据**: 最近1小时的使用趋势

#### 3.1.2 内存监控
- **指标**: 
  - 已用内存 / 总内存
  - 内存压力状态
  - 可用内存
- **更新频率**: 1秒
- **展示方式**: 百分比 + 柱状图
- **单位**: GB/MB自动切换

#### 3.1.3 存储空间监控
- **指标**:
  - 已用存储 / 总存储
  - 可用空间
  - 各类型文件占用（照片、应用、系统等）
- **更新频率**: 30秒
- **展示方式**: 百分比 + 饼图

#### 3.1.4 网络速度监控
- **指标**:
  - 实时下载速度
  - 实时上传速度
  - 累计流量统计
- **更新频率**: 1秒
- **展示方式**: KB/s, MB/s自动切换
- **网络类型**: WiFi/蜂窝网络区分

### 3.2 界面功能

#### 3.2.1 主应用界面
- 仪表盘总览
- 详细性能图表
- 历史数据查看
- 设置和配置

#### 3.2.2 桌面小组件
- 小尺寸：单一指标显示
- 中尺寸：2-3个核心指标
- 大尺寸：完整仪表盘

#### 3.2.3 菜单栏常驻
- 实时数据显示
- 快速访问主应用
- 简化的设置选项

## 4. 系统API和权限

### 4.1 需要的系统API
```swift
// CPU信息
import Darwin
import Foundation

// 内存信息
import os.proc
import mach

// 存储信息
import Foundation (FileManager)

// 网络信息
import Network
import SystemConfiguration
```

### 4.2 权限要求
- **网络访问权限**: 监控网络使用情况
- **后台刷新权限**: 保持数据更新
- **通知权限**: 性能警告通知（可选）

### 4.3 隐私考虑
- 所有数据仅在本地处理，不上传到服务器
- 遵循Apple隐私指南
- 透明的数据使用说明

## 5. 性能要求

### 5.1 性能指标
- **内存占用**: < 50MB
- **CPU占用**: < 5%（后台运行时）
- **电池影响**: 最小化
- **启动时间**: < 2秒

### 5.2 优化策略
- 使用高效的数据采集算法
- 智能的更新频率调节
- 后台任务优化
- 内存管理优化

## 6. 用户体验设计

### 6.1 设计原则
- **简洁性**: 信息清晰，界面简洁
- **实时性**: 数据更新及时
- **一致性**: 各界面风格统一
- **可访问性**: 支持辅助功能

### 6.2 视觉设计
- **色彩方案**: 
  - 主色调：系统蓝色 (#007AFF)
  - 警告色：橙色 (#FF9500)
  - 危险色：红色 (#FF3B30)
  - 成功色：绿色 (#34C759)
- **字体**: SF Pro系统字体
- **图标**: SF Symbols
- **动画**: 流畅的过渡动画

## 7. 开发计划

### 7.1 开发阶段
1. **阶段1**: 核心数据采集功能 (2周)
2. **阶段2**: 主应用界面开发 (2周)
3. **阶段3**: 小组件开发 (1周)
4. **阶段4**: 菜单栏功能 (1周)
5. **阶段5**: 测试和优化 (1周)

### 7.2 技术风险
- iOS系统API限制
- 小组件刷新频率限制
- 不同设备兼容性
- App Store审核要求

## 8. 商业模式

### 8.1 定价策略
- **免费版**: 基础监控功能
- **专业版**: 完整功能 + 历史数据 + 高级设置
- **价格**: $4.99一次性购买

### 8.2 盈利点
- 应用内购买
- 专业功能解锁
- 无广告体验

## 9. 技术实现细节

### 9.1 数据采集实现

#### 9.1.1 CPU使用率获取
```swift
import Darwin

class CPUMonitor {
    func getCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / Double(ProcessInfo.processInfo.physicalMemory) * 100
        }
        return 0.0
    }
}
```

#### 9.1.2 内存信息获取
```swift
import os.proc

class MemoryMonitor {
    func getMemoryInfo() -> (used: UInt64, total: UInt64, pressure: String) {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory

        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO),
                 UnsafeMutablePointer<integer_t>(&info), &count)

        let usedMemory = UInt64(info.resident_size)
        let pressure = getMemoryPressure()

        return (used: usedMemory, total: physicalMemory, pressure: pressure)
    }

    private func getMemoryPressure() -> String {
        // 实现内存压力检测逻辑
        return "Normal"
    }
}
```

#### 9.1.3 存储空间监控
```swift
import Foundation

class StorageMonitor {
    func getStorageInfo() -> (used: UInt64, total: UInt64, available: UInt64) {
        let fileManager = FileManager.default

        do {
            let attributes = try fileManager.attributesOfFileSystem(forPath: "/")
            let totalSpace = attributes[.systemSize] as? UInt64 ?? 0
            let freeSpace = attributes[.systemFreeSize] as? UInt64 ?? 0
            let usedSpace = totalSpace - freeSpace

            return (used: usedSpace, total: totalSpace, available: freeSpace)
        } catch {
            return (used: 0, total: 0, available: 0)
        }
    }
}
```

#### 9.1.4 网络速度监控
```swift
import Network
import SystemConfiguration

class NetworkMonitor {
    private var previousBytes: (rx: UInt64, tx: UInt64) = (0, 0)
    private var lastUpdateTime = Date()

    func getNetworkSpeed() -> (download: Double, upload: Double) {
        let currentBytes = getNetworkBytes()
        let currentTime = Date()
        let timeDiff = currentTime.timeIntervalSince(lastUpdateTime)

        let downloadSpeed = Double(currentBytes.rx - previousBytes.rx) / timeDiff
        let uploadSpeed = Double(currentBytes.tx - previousBytes.tx) / timeDiff

        previousBytes = currentBytes
        lastUpdateTime = currentTime

        return (download: downloadSpeed, upload: uploadSpeed)
    }

    private func getNetworkBytes() -> (rx: UInt64, tx: UInt64) {
        // 实现网络字节数获取逻辑
        return (rx: 0, tx: 0)
    }
}
```

### 9.2 小组件实现

#### 9.2.1 WidgetKit配置
```swift
import WidgetKit
import SwiftUI

@main
struct SystemMonitorWidget: Widget {
    let kind: String = "SystemMonitorWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            SystemMonitorWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("系统监控")
        .description("实时显示系统性能指标")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), cpuUsage: 0.6, memoryUsage: 4.2, networkSpeed: 12.5)
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), cpuUsage: 0.6, memoryUsage: 4.2, networkSpeed: 12.5)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        // 实现时间线更新逻辑
    }
}
```

### 9.3 菜单栏实现
```swift
import SwiftUI

@main
struct MenuBarApp: App {
    @StateObject private var systemMonitor = SystemMonitor()

    var body: some Scene {
        MenuBarExtra("SystemMonitor", systemImage: "cpu") {
            MenuBarView()
                .environmentObject(systemMonitor)
        }
        .menuBarExtraStyle(.window)
    }
}

struct MenuBarView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("CPU: \(systemMonitor.cpuUsage, specifier: "%.1f")%")
                Spacer()
                Circle()
                    .fill(systemMonitor.cpuUsage > 80 ? .red : .green)
                    .frame(width: 8, height: 8)
            }

            HStack {
                Text("内存: \(systemMonitor.memoryUsage, specifier: "%.1f")GB")
                Spacer()
                Circle()
                    .fill(systemMonitor.memoryUsage > 6 ? .red : .green)
                    .frame(width: 8, height: 8)
            }

            Divider()

            Button("打开主应用") {
                // 打开主应用逻辑
            }

            Button("退出") {
                NSApplication.shared.terminate(nil)
            }
        }
        .padding()
        .frame(width: 200)
    }
}
```

## 10. 测试策略

### 10.1 单元测试
- 数据采集模块测试
- 计算逻辑准确性测试
- 边界条件测试

### 10.2 集成测试
- 小组件与主应用数据同步测试
- 菜单栏功能测试
- 后台刷新测试

### 10.3 性能测试
- 内存泄漏检测
- CPU占用监控
- 电池消耗测试
- 长时间运行稳定性测试

### 10.4 兼容性测试
- 不同iOS版本测试
- 不同设备型号测试
- 深色/浅色模式测试

## 11. 发布和维护

### 11.1 App Store发布
- 应用描述和截图准备
- 隐私政策制定
- 审核指南遵循
- 关键词优化

### 11.2 版本迭代计划
- **v1.0**: 基础功能发布
- **v1.1**: 用户反馈优化
- **v1.2**: 新增历史数据图表
- **v2.0**: 支持更多系统指标

### 11.3 用户支持
- 应用内帮助文档
- 常见问题解答
- 用户反馈收集机制
- 技术支持邮箱
