<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SystemMonitor Pro - UI原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f2f2f7;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .prototype-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            border-bottom: 2px solid #007AFF;
            padding-bottom: 10px;
        }
        
        /* 主应用界面 */
        .main-app {
            width: 375px;
            height: 812px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 25px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .app-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .app-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .app-subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .progress-ring {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(#34C759 0deg 216deg, rgba(255,255,255,0.2) 216deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px auto;
            position: relative;
        }
        
        .progress-ring::before {
            content: '';
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            position: absolute;
        }
        
        .progress-text {
            font-size: 16px;
            font-weight: 600;
            z-index: 1;
        }
        
        /* 小组件样式 */
        .widgets-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .widget {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .widget-small {
            width: 155px;
            height: 155px;
        }
        
        .widget-medium {
            width: 329px;
            height: 155px;
        }
        
        .widget-large {
            width: 329px;
            height: 329px;
        }
        
        .widget-title {
            font-size: 14px;
            font-weight: 600;
            color: #8e8e93;
            margin-bottom: 8px;
        }
        
        .widget-value {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 4px;
        }
        
        .widget-subtitle {
            font-size: 12px;
            color: #8e8e93;
        }
        
        /* 菜单栏样式 */
        .menubar {
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(20px);
            border-radius: 8px;
            padding: 8px 12px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .menubar-icon {
            width: 16px;
            height: 16px;
            background: #007AFF;
            border-radius: 2px;
        }
        
        .menubar-text {
            font-size: 13px;
            font-weight: 500;
            color: #1d1d1f;
        }
        
        .chart-container {
            height: 60px;
            background: linear-gradient(to right, #34C759, #007AFF);
            border-radius: 8px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }
        
        .chart-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: white;
            opacity: 0.8;
        }
        
        .network-indicator {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .network-speed {
            font-size: 12px;
            color: #8e8e93;
        }
        
        .speed-value {
            font-weight: 600;
            color: #1d1d1f;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主应用界面原型 -->
        <div class="prototype-section">
            <h2 class="section-title">📱 主应用界面</h2>
            <div class="main-app">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●●○○</span>
                    <span>100%🔋</span>
                </div>
                
                <div class="app-header">
                    <div class="app-title">SystemMonitor</div>
                    <div class="app-subtitle">实时性能监控</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="progress-ring">
                            <div class="progress-text">60%</div>
                        </div>
                        <div class="metric-label">CPU使用率</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-value">4.2</div>
                        <div class="metric-label">内存 GB</div>
                        <div style="font-size: 12px; opacity: 0.7;">共8GB</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-value">128</div>
                        <div class="metric-label">可用空间 GB</div>
                        <div style="font-size: 12px; opacity: 0.7;">共256GB</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-value">12.5</div>
                        <div class="metric-label">网速 MB/s</div>
                        <div style="font-size: 12px; opacity: 0.7;">↓ 12.5 ↑ 2.1</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-line"></div>
                </div>
                
                <div style="text-align: center; color: white; margin-top: 20px;">
                    <div style="font-size: 14px; opacity: 0.8;">点击查看详细信息</div>
                </div>
            </div>
        </div>
        
        <!-- 桌面小组件原型 -->
        <div class="prototype-section">
            <h2 class="section-title">🏠 桌面小组件</h2>
            <div class="widgets-container">
                <!-- 小尺寸小组件 -->
                <div class="widget widget-small">
                    <div class="widget-title">CPU</div>
                    <div class="progress-ring" style="width: 60px; height: 60px; margin: 20px auto;">
                        <div class="progress-text" style="color: #1d1d1f;">60%</div>
                    </div>
                    <div class="widget-subtitle">使用率</div>
                </div>
                
                <!-- 中尺寸小组件 -->
                <div class="widget widget-medium">
                    <div class="widget-title">系统状态</div>
                    <div style="display: flex; justify-content: space-between; align-items: center; height: 100px;">
                        <div style="text-align: center;">
                            <div class="widget-value" style="font-size: 20px;">60%</div>
                            <div class="widget-subtitle">CPU</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="widget-value" style="font-size: 20px;">4.2GB</div>
                            <div class="widget-subtitle">内存</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="widget-value" style="font-size: 20px;">12.5</div>
                            <div class="widget-subtitle">MB/s</div>
                        </div>
                    </div>
                </div>
                
                <!-- 大尺寸小组件 -->
                <div class="widget widget-large">
                    <div class="widget-title">完整监控面板</div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
                        <div style="text-align: center;">
                            <div class="progress-ring" style="width: 50px; height: 50px; margin: 10px auto;">
                                <div class="progress-text" style="color: #1d1d1f; font-size: 12px;">60%</div>
                            </div>
                            <div class="widget-subtitle">CPU</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="widget-value" style="font-size: 18px;">4.2GB</div>
                            <div class="widget-subtitle">内存使用</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="widget-value" style="font-size: 18px;">128GB</div>
                            <div class="widget-subtitle">可用空间</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="widget-value" style="font-size: 18px;">12.5</div>
                            <div class="widget-subtitle">网速 MB/s</div>
                        </div>
                    </div>
                    <div class="chart-container" style="height: 40px; margin-top: 15px;">
                        <div class="chart-line"></div>
                    </div>
                    <div class="network-indicator">
                        <div class="network-speed">下载: <span class="speed-value">12.5 MB/s</span></div>
                        <div class="network-speed">上传: <span class="speed-value">2.1 MB/s</span></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 菜单栏原型 -->
        <div class="prototype-section">
            <h2 class="section-title">📊 菜单栏常驻</h2>
            <div style="text-align: center; padding: 20px;">
                <div class="menubar">
                    <div class="menubar-icon"></div>
                    <span class="menubar-text">CPU: 60%</span>
                    <span class="menubar-text">|</span>
                    <span class="menubar-text">内存: 4.2GB</span>
                    <span class="menubar-text">|</span>
                    <span class="menubar-text">↓12.5MB/s</span>
                </div>
                
                <div style="margin-top: 20px; color: #8e8e93; font-size: 14px;">
                    点击菜单栏图标展开详细信息面板
                </div>
            </div>
        </div>
        
        <!-- 设计说明 -->
        <div class="prototype-section">
            <h2 class="section-title">🎨 设计说明</h2>
            <div style="line-height: 1.6; color: #1d1d1f;">
                <h3 style="margin-bottom: 10px;">色彩方案</h3>
                <ul style="margin-bottom: 20px; padding-left: 20px;">
                    <li><strong>主色调:</strong> #007AFF (系统蓝)</li>
                    <li><strong>成功色:</strong> #34C759 (绿色) - 正常状态</li>
                    <li><strong>警告色:</strong> #FF9500 (橙色) - 中等负载</li>
                    <li><strong>危险色:</strong> #FF3B30 (红色) - 高负载</li>
                </ul>
                
                <h3 style="margin-bottom: 10px;">交互特性</h3>
                <ul style="margin-bottom: 20px; padding-left: 20px;">
                    <li>实时数据更新，1秒刷新频率</li>
                    <li>点击小组件跳转到主应用对应页面</li>
                    <li>长按小组件显示快捷操作菜单</li>
                    <li>菜单栏支持鼠标悬停显示详细信息</li>
                </ul>
                
                <h3 style="margin-bottom: 10px;">响应式设计</h3>
                <ul style="padding-left: 20px;">
                    <li>支持深色模式自动切换</li>
                    <li>适配不同屏幕尺寸</li>
                    <li>支持动态字体大小</li>
                    <li>无障碍访问优化</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
